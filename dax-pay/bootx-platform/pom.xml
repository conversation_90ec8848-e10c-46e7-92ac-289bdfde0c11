<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.3</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>cn.bootx.platform</groupId>
    <artifactId>bootx-platform</artifactId>
    <version>3.0.13</version>
    <packaging>pom</packaging>
    <description>基础脚手架服务</description>

    <modules>
        <module>bootx-platform-core</module>
        <module>bootx-platform-common</module>
        <module>bootx-platform-starter</module>
        <module>bootx-platform-service</module>
    </modules>

    <properties>
        <bootx-platform.version>3.0.13</bootx-platform.version>
        <!-- 再高的的新版本与会knife4j 4.5冲突, 目前使用三方的knife4j依赖 -->
        <springdoc.version>2.7.0</springdoc.version>
        <hutool.version>5.8.31</hutool.version>
        <bouncycastle.version>1.78.1</bouncycastle.version>
        <knife4j.version>4.6.0</knife4j.version>
        <mybatis-plus.version>3.5.9</mybatis-plus.version>
        <mybatis-plus-join.version>1.5.4</mybatis-plus-join.version>
        <dynamic-datasource.version>4.3.1</dynamic-datasource.version>
        <lock4j.version>2.2.7</lock4j.version>
        <x-file-storage.version>2.2.1</x-file-storage.version>
        <minio.version>8.5.4</minio.version>
        <qcloud.version>5.6.137</qcloud.version>
        <sa-token.version>1.39.0</sa-token.version>
        <justauth.version>1.16.6</justauth.version>
        <caffeine.version>3.1.8</caffeine.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <ip2region.version>2.7.0</ip2region.version>
        <lombok-mapstruct.version>0.2.0</lombok-mapstruct.version>
        <ttl.version>2.14.5</ttl.version>
        <kryo.version>5.6.0</kryo.version>
        <autopoi.version>1.4.8</autopoi.version>
        <easypoi.version>4.5.0</easypoi.version>
        <easytrans.version>3.1.2</easytrans.version>

        <java.version>21</java.version>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
    </properties>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <compilerArgs>
                        <arg>-Xlint:-options</arg>
                    </compilerArgs>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- 打包Excel等资源文件损坏问题 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                        <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>doc</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <!-- 指定打包资源路径 -->
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <!-- java类路径中会被打包的软件 -->
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.sql</include>
                    <include>**/*.flt</include>
                    <include>**/*.xlsx</include>
                    <include>**/*.xls</include>
                    <include>**/*.docx</include>
                    <include>**/*.doc</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>
