package org.dromara.daxpay.mall.dao;

import cn.bootx.platform.common.mybatisplus.util.MpUtil;
import cn.bootx.platform.common.mybatisplus.query.generator.QueryGenerator;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.dromara.daxpay.mall.entity.AuditLog;
import org.dromara.daxpay.mall.param.AuditLogPageParam;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 审计日志Mapper
 * <AUTHOR>
 * @since 2024/7/30
 */
@Mapper
public interface AuditLogMapper extends BaseMapper<AuditLog> {

    /**
     * 分页查询
     */
    default Page<AuditLog> page(AuditLogPageParam param, Page<AuditLog> mpPage) {
        QueryWrapper<AuditLog> generator = QueryGenerator.generator(param, AuditLog.class);
        return this.selectPage(mpPage, generator);
    }

    /**
     * 根据用户ID查询审计日志
     */
    default List<AuditLog> findByUserId(Long userId) {
        return this.selectList(new QueryWrapper<AuditLog>()
                .eq(MpUtil.getColumnName(AuditLog::getUserId), userId)
                .orderByDesc(MpUtil.getColumnName(AuditLog::getOperationTime)));
    }

    /**
     * 根据操作类型查询审计日志
     */
    default List<AuditLog> findByOperationType(String operationType) {
        return this.selectList(new QueryWrapper<AuditLog>()
                .eq(MpUtil.getColumnName(AuditLog::getOperationType), operationType)
                .orderByDesc(MpUtil.getColumnName(AuditLog::getOperationTime)));
    }

    /**
     * 清理过期的审计日志
     */
    default int deleteExpiredLogs(LocalDateTime expireTime) {
        return this.delete(new QueryWrapper<AuditLog>()
                .lt(MpUtil.getColumnName(AuditLog::getOperationTime), expireTime));
    }
}