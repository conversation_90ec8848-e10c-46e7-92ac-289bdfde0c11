package org.dromara.daxpay.mall.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bootx.platform.common.mybatisplus.util.MpUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.dromara.daxpay.mall.entity.MallCart;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 商城购物车Mapper
 * <AUTHOR>
 * @since 2024/7/26
 */
@Mapper
public interface MallCartMapper extends BaseMapper<MallCart> {

    /**
     * 根据用户ID查询购物车
     */
    default List<MallCart> findByUserId(Long userId) {
        return this.selectList(new QueryWrapper<MallCart>()
                .eq(MpUtil.getColumnName(MallCart::getUserId), userId)
                .orderByDesc(MpUtil.getColumnName(MallCart::getCreateTime)));
    }

    /**
     * 根据用户ID和商户ID查询购物车
     */
    default List<MallCart> findByUserIdAndMerchantId(Long userId, Long merchantId) {
        return this.selectList(new QueryWrapper<MallCart>()
                .eq(MpUtil.getColumnName(MallCart::getUserId), userId)
                .eq(MpUtil.getColumnName(MallCart::getMerchantId), merchantId)
                .orderByDesc(MpUtil.getColumnName(MallCart::getCreateTime)));
    }

    /**
     * 根据用户ID和商品ID查询购物车项
     */
    default MallCart findByUserIdAndProductId(Long userId, Long productId, String specsHash) {
        QueryWrapper<MallCart> wrapper = new QueryWrapper<MallCart>()
                .eq(MpUtil.getColumnName(MallCart::getUserId), userId)
                .eq(MpUtil.getColumnName(MallCart::getProductId), productId);
        
        // 如果有规格哈希，则精确匹配
        if (specsHash != null) {
            wrapper.eq("MD5(CAST(product_specs AS CHAR))", specsHash);
        } else {
            wrapper.isNull(MpUtil.getColumnName(MallCart::getProductSpecs));
        }
        
        return this.selectOne(wrapper);
    }

    /**
     * 根据用户ID获取选中的购物车项
     */
    default List<MallCart> findSelectedByUserId(Long userId) {
        return this.selectList(new QueryWrapper<MallCart>()
                .eq(MpUtil.getColumnName(MallCart::getUserId), userId)
                .eq(MpUtil.getColumnName(MallCart::getSelected), true)
                .orderByDesc(MpUtil.getColumnName(MallCart::getCreateTime)));
    }

    /**
     * 清理过期的购物车数据
     */
    default int cleanExpiredCart(LocalDateTime expireTime) {
        return this.delete(new QueryWrapper<MallCart>()
                .lt(MpUtil.getColumnName(MallCart::getLastModifiedTime), expireTime));
    }

    /**
     * 根据商品ID删除购物车项
     */
    default int deleteByProductId(Long productId) {
        return this.delete(new QueryWrapper<MallCart>()
                .eq(MpUtil.getColumnName(MallCart::getProductId), productId));
    }

    /**
     * 统计用户购物车商品数量
     */
    default int countByUserId(Long userId) {
        return Math.toIntExact(this.selectCount(new QueryWrapper<MallCart>()
                .eq(MpUtil.getColumnName(MallCart::getUserId), userId)));
    }
}