package org.dromara.daxpay.mall.dao;

import cn.bootx.platform.common.mybatisplus.util.MpUtil;
import cn.bootx.platform.common.mybatisplus.query.generator.QueryGenerator;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.dromara.daxpay.mall.entity.MallCategory;
import org.dromara.daxpay.mall.param.MallCategoryPageParam;

import java.util.List;

/**
 * 商城商品分类Mapper
 * <AUTHOR>
 * @since 2024/7/26
 */
@Mapper
public interface MallCategoryMapper extends BaseMapper<MallCategory> {

    /**
     * 分页查询
     */
    default Page<MallCategory> page(MallCategoryPageParam param, Page<MallCategory> mpPage) {
        QueryWrapper<MallCategory> generator = QueryGenerator.generator(param, MallCategory.class);
        return this.selectPage(mpPage, generator);
    }

    /**
     * 根据父分类ID查询子分类
     */
    default List<MallCategory> findByParentId(Long parentId) {
        return this.selectList(new QueryWrapper<MallCategory>()
                .eq(MpUtil.getColumnName(MallCategory::getParentId), parentId)
                .eq(MpUtil.getColumnName(MallCategory::getStatus), "ACTIVE")
                .orderByAsc(MpUtil.getColumnName(MallCategory::getSortOrder)));
    }

    /**
     * 根据商户ID查询所有分类
     */
    default List<MallCategory> findByMerchantId(Long merchantId) {
        return this.selectList(new QueryWrapper<MallCategory>()
                .eq(MpUtil.getColumnName(MallCategory::getMerchantId), merchantId)
                .eq(MpUtil.getColumnName(MallCategory::getStatus), "ACTIVE")
                .orderByAsc(MpUtil.getColumnName(MallCategory::getLevel))
                .orderByAsc(MpUtil.getColumnName(MallCategory::getSortOrder)));
    }

    /**
     * 检查分类名称是否存在
     */
    default boolean existsByNameAndParent(Long merchantId, String categoryName, Long parentId, Long excludeId) {
        QueryWrapper<MallCategory> wrapper = new QueryWrapper<MallCategory>()
                .eq(MpUtil.getColumnName(MallCategory::getMerchantId), merchantId)
                .eq(MpUtil.getColumnName(MallCategory::getCategoryName), categoryName)
                .eq(MpUtil.getColumnName(MallCategory::getParentId), parentId);
        
        if (excludeId != null) {
            wrapper.ne(MpUtil.getColumnName(MallCategory::getId), excludeId);
        }
        
        return this.selectCount(wrapper) > 0;
    }
}