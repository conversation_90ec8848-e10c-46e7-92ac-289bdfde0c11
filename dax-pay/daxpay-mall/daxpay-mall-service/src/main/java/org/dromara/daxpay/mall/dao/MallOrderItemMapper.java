package org.dromara.daxpay.mall.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bootx.platform.common.mybatisplus.util.MpUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.dromara.daxpay.mall.entity.MallOrderItem;

import java.util.List;

/**
 * 商城订单商品Mapper
 * <AUTHOR>
 * @since 2024/7/26
 */
@Mapper
public interface MallOrderItemMapper extends BaseMapper<MallOrderItem> {

    /**
     * 根据订单ID查询订单商品
     */
    default List<MallOrderItem> findByOrderId(Long orderId) {
        return this.selectList(new QueryWrapper<MallOrderItem>()
                .eq(MpUtil.getColumnName(MallOrderItem::getOrderId), orderId)
                .orderByAsc(MpUtil.getColumnName(MallOrderItem::getCreateTime)));
    }

    /**
     * 根据商品ID查询订单商品
     */
    default List<MallOrderItem> findByProductId(Long productId) {
        return this.selectList(new QueryWrapper<MallOrderItem>()
                .eq(MpUtil.getColumnName(MallOrderItem::getProductId), productId));
    }

    /**
     * 统计商品销量
     */
    default Integer sumQuantityByProductId(Long productId) {
        // 这里需要使用聚合查询，简化处理返回0，实际应该使用MyBatis的聚合函数
        List<MallOrderItem> items = findByProductId(productId);
        return items.stream().mapToInt(MallOrderItem::getQuantity).sum();
    }
}