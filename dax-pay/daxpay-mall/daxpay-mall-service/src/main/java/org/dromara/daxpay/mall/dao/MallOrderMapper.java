package org.dromara.daxpay.mall.dao;

import cn.bootx.platform.common.mybatisplus.util.MpUtil;
import cn.bootx.platform.common.mybatisplus.query.generator.QueryGenerator;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.dromara.daxpay.mall.entity.MallOrder;
import org.dromara.daxpay.mall.param.MallOrderPageParam;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商城订单Mapper
 * <AUTHOR>
 * @since 2024/7/26
 */
@Mapper
public interface MallOrderMapper extends BaseMapper<MallOrder> {

    /**
     * 分页查询
     */
    default Page<MallOrder> page(MallOrderPageParam param, Page<MallOrder> mpPage) {
        QueryWrapper<MallOrder> generator = QueryGenerator.generator(param, MallOrder.class);
        return this.selectPage(mpPage, generator);
    }

    /**
     * 根据订单号查询
     */
    default MallOrder findByOrderNo(String orderNo) {
        return this.selectOne(new QueryWrapper<MallOrder>()
                .eq(MpUtil.getColumnName(MallOrder::getOrderNo), orderNo));
    }

    /**
     * 根据支付订单ID查询
     */
    default MallOrder findByPayOrderId(Long payOrderId) {
        return this.selectOne(new QueryWrapper<MallOrder>()
                .eq(MpUtil.getColumnName(MallOrder::getPayOrderId), payOrderId));
    }

    /**
     * 根据用户ID查询订单列表
     */
    default List<MallOrder> findByUserId(Long userId) {
        return this.selectList(new QueryWrapper<MallOrder>()
                .eq(MpUtil.getColumnName(MallOrder::getUserId), userId)
                .orderByDesc(MpUtil.getColumnName(MallOrder::getCreateTime)));
    }

    /**
     * 根据商户ID和状态统计订单数量
     */
    default long countByMerchantIdAndStatus(Long merchantId, String orderStatus) {
        return this.selectCount(new QueryWrapper<MallOrder>()
                .eq(MpUtil.getColumnName(MallOrder::getMerchantId), merchantId)
                .eq(MpUtil.getColumnName(MallOrder::getOrderStatus), orderStatus));
    }

    /**
     * 统计商户销售额
     */
    default BigDecimal sumSalesByMerchantId(Long merchantId, LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<MallOrder> wrapper = new QueryWrapper<MallOrder>()
                .eq(MpUtil.getColumnName(MallOrder::getMerchantId), merchantId)
                .eq(MpUtil.getColumnName(MallOrder::getPayStatus), "PAID");
        
        if (startTime != null) {
            wrapper.ge(MpUtil.getColumnName(MallOrder::getCreateTime), startTime);
        }
        if (endTime != null) {
            wrapper.le(MpUtil.getColumnName(MallOrder::getCreateTime), endTime);
        }
        
        // 这里需要使用聚合查询，简化处理返回null，实际应该使用MyBatis的聚合函数
        return null;
    }

    /**
     * 查询待支付超时订单
     */
    default List<MallOrder> findTimeoutUnpaidOrders(LocalDateTime timeoutTime) {
        return this.selectList(new QueryWrapper<MallOrder>()
                .eq(MpUtil.getColumnName(MallOrder::getOrderStatus), "PENDING")
                .eq(MpUtil.getColumnName(MallOrder::getPayStatus), "UNPAID")
                .lt(MpUtil.getColumnName(MallOrder::getCreateTime), timeoutTime));
    }
}