package org.dromara.daxpay.mall.dao;

import cn.bootx.platform.common.mybatisplus.util.MpUtil;
import cn.bootx.platform.common.mybatisplus.query.generator.QueryGenerator;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.dromara.daxpay.mall.entity.MallProduct;
import org.dromara.daxpay.mall.param.MallProductPageParam;

/**
 * 商城商品Mapper
 * <AUTHOR>
 * @since 2024/7/26
 */
@Mapper
public interface MallProductMapper extends BaseMapper<MallProduct> {

    /**
     * 分页查询
     */
    default Page<MallProduct> page(MallProductPageParam param, Page<MallProduct> mpPage) {
        QueryWrapper<MallProduct> generator = QueryGenerator.generator(param, MallProduct.class);
        return this.selectPage(mpPage, generator);
    }

    /**
     * 根据商户ID和状态查询商品数量
     */
    default long countByMerchantIdAndStatus(Long merchantId, String status) {
        return this.selectCount(new QueryWrapper<MallProduct>()
                .eq(MpUtil.getColumnName(MallProduct::getMerchantId), merchantId)
                .eq(MpUtil.getColumnName(MallProduct::getStatus), status));
    }

    /**
     * 根据分类ID查询商品数量
     */
    default long countByCategoryId(Long categoryId) {
        return this.selectCount(new QueryWrapper<MallProduct>()
                .eq(MpUtil.getColumnName(MallProduct::getCategoryId), categoryId));
    }

    /**
     * 查询库存不足的商品
     */
    default Page<MallProduct> findLowStockProducts(Long merchantId, Page<MallProduct> mpPage) {
        QueryWrapper<MallProduct> wrapper = new QueryWrapper<MallProduct>()
                .eq(MpUtil.getColumnName(MallProduct::getMerchantId), merchantId)
                .eq(MpUtil.getColumnName(MallProduct::getStockEnabled), true)
                .le("stock", "stock_warning_threshold")
                .eq(MpUtil.getColumnName(MallProduct::getStatus), "ACTIVE");
        return this.selectPage(mpPage, wrapper);
    }
}