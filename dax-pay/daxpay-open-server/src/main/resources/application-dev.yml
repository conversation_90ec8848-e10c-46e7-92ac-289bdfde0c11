spring:
  # 允许Bean定义覆盖，解决缓存管理器冲突
  main:
    allow-bean-definition-overriding: true
  # 解决Spring Boot 3.x与MyBatis-Plus Join扩展兼容性问题
  sql:
    init:
      dependency-detection:
        enabled: false
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          # MySQL连接
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************************************************************
          username: pay
          password: elt4RTckgPx.xgdg
      hikari:
        keepalive-time: 300000
        minimum-idle: 5
        maximum-pool-size: 20
        auto-commit: true
        idle-timeout: 30000
        pool-name: DaxPayHikariCP
        max-lifetime: 900000
        connection-timeout: 30000
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      database: 0
      username: default
      password: "gyuan@lou"
      timeout: 10s
      lettuce:
        pool:
          max-wait: 1000ms
          max-active: 8
          max-idle: 8
          min-idle: 0
  # JPA配置
  jpa:
    open-in-view: false
  # 热部署配置（如果使用 spring-boot-devtools）
  devtools:
    restart:
      enabled: true
      additional-paths: src/main/java
    livereload:
      enabled: true

# 开发时显示debug日志
logging:
  level:
    cn.bootx.**: debug
    org.dromara.daxpay.**: debug
    org.springframework.jdbc.core: debug
  file:
    name: ./logs/daxpay.log

# 接口文档配置
springdoc:
  # 默认展开对象类型的属性, 主要用在get类型的参数中
  default-flat-param-object: true

# 基础脚手架配置
bootx-platform:
  common:
    # swagger相关配置
    swagger:
      author: DaxPay
      title: DaxPay支付平台
      description: DaxPay支付平台-开源版
      version: 0.0.1
      base-packages:
        "[BootxPlatform接口]":
          - cn.bootx.platform.common
          - cn.bootx.platform.starter
          - cn.bootx.platform.iam
          - cn.bootx.platform.baseapi
          - cn.bootx.platform.notice
        "[支付平台接口]":
          - org.dromara.daxpay.controller
        "[支付通道接口]":
          - org.dromara.daxpay.channel
  starter:
    auth:
      enable-admin: true
      ignore-urls:
        - '/actuator/**'
        - '/v3/api-docs/**'
        - '/doc.html'
        - '/swagger-resources/**'
        - '/token/**'
        - '/ws/**'
        - '/demo/**'
        - '/test/**'
        - '/webjars/**'
        - '/front/**'
        - '/h5/**'
        - '/css/**'
        - '/error'
        - '/favicon.ico'
    file-upload:
      # 使用后端代理访问, 线上请使用 Nginx 配置或者直连方式，效率更高
      forward-server-url: http://127.0.0.1:9999
      file-server-url: http://127.0.0.1:9999

# DaxPay配置
dax-pay:
  env: DEV_
  machine-no: 70

# USDT 通道配置（基于实际的配置属性类）
daxpay:
  channel:
    usdt:
      # 启用 USDT 通道
      enabled: true
      # 默认过期时间(分钟)
      default-expiry-minutes: 30
      # 监控间隔(秒)
      monitor-interval-seconds: 30
      # 禁用USDT专用缓存管理器，避免与主缓存管理器冲突
      cache:
        enabled: false
      # 网络配置
      networks:
        TRC20:
          name: "TRON测试网"
          chain-id: "0x2b6653dc"
          rpc-urls:
            - "https://api.shasta.trongrid.io"
          contract-address: "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"
          confirmation-blocks: 1
          gas-limit: 21000
          gas-price: "20000000000"
          enabled: true
        ERC20:
          name: "以太坊测试网"
          chain-id: "0x5"
          rpc-urls:
            - "https://goerli.infura.io/v3/YOUR_PROJECT_ID"
          contract-address: "0x509Ee0d083DdF8AC028f2a56731412edD63223B9"
          confirmation-blocks: 3
          gas-limit: 21000
          gas-price: "20000000000"
          enabled: true

# Mall JWT 配置（基于实际的代码需求，但由于 Mall 模块未集成，这些配置暂时不会生效）
mall:
  jwt:
    secret: daxpay-mall-secret-key-2024
    header: Authorization
    prefix: "Bearer "

# 文件存储配置
dromara:
  # 注意, 不要设置 domain 访问路径, 自行进行拼接访问路径, 来保证可迁移性
  x-file-storage:
    default-platform: local
    # 使用Nginx映射到存储路径, 然后将nginx的地址设置到 bootx-platform.starter.file-upload.file-server-url参数
    local-plus:
      - platform: local
        enable-storage: true
        base-path: /file/ # 基础路径
        storage-path: ./uploads # 存储路径 - 使用相对路径
    # 将 minio访问地址+桶名称 进行组合, 然后设置到 bootx-platform.starter.file-upload.file-server-url
    # 例如 minio地址 http://127.0.0.1:9001  桶名称 daxpay, 拼接后的地址为 http://127.0.0.1:9001/daxpay/
    minio:
      - platform: minio
        enable-storage: true
        access-key: yDAArSoyQAligC2IGf7C
        secret-key: vDgpt5R4kR2UCapMzx32Rb6qZegok21dRsU6XJ1j
        end-point: http://127.0.0.1:9002  # minio访问地址
        bucket-name: daxpay # 存储桶名称
        base-path: /file/ # 基础存储路径

# 开发工具配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

# 开发环境特殊配置
debug: true
