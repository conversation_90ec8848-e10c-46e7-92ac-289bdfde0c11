<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.dromara.daxpay</groupId>
    <artifactId>daxpay</artifactId>
    <version>3.0.13</version>
    <packaging>pom</packaging>

    <name>dax-pay-plus</name>
    <description>DaxPay开源版支付系统</description>

    <modules>
        <module>bootx-platform</module>
        <module>daxpay-open</module>
        <module>daxpay-open-channel</module>
        <module>daxpay-open-server</module>
        <module>daxpay-open-sdk</module>
    </modules>
    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!-- 版本管理 -->
        <daxpay.version>3.0.13</daxpay.version>
        <bootx-platform.version>3.0.13</bootx-platform.version>

        <!-- 第三方依赖版本 -->
        <lombok.version>1.18.30</lombok.version>
        <mybatis-plus.version>3.5.9</mybatis-plus.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- DaxPay模块版本管理 -->
            <dependency>
                <groupId>org.dromara.daxpay</groupId>
                <artifactId>daxpay-open-core</artifactId>
                <version>${daxpay.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.daxpay</groupId>
                <artifactId>daxpay-open-service</artifactId>
                <version>${daxpay.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.daxpay</groupId>
                <artifactId>daxpay-open-controller</artifactId>
                <version>${daxpay.version}</version>
            </dependency>

            <!-- 支付渠道模块版本管理 -->
            <dependency>
                <groupId>org.dromara.daxpay</groupId>
                <artifactId>daxpay-open-channel-alipay</artifactId>
                <version>${daxpay.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.daxpay</groupId>
                <artifactId>daxpay-open-channel-wechat</artifactId>
                <version>${daxpay.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.daxpay</groupId>
                <artifactId>daxpay-open-channel-union</artifactId>
                <version>${daxpay.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.daxpay</groupId>
                <artifactId>daxpay-open-channel-usdt</artifactId>
                <version>${daxpay.version}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.daxpay</groupId>
                <artifactId>daxpay-mall-core</artifactId>
                <version>${daxpay.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.daxpay</groupId>
                <artifactId>daxpay-mall-service</artifactId>
                <version>${daxpay.version}</version>
            </dependency>

            <!-- BootX平台模块版本管理 -->
            <dependency>
                <groupId>cn.bootx.platform</groupId>
                <artifactId>bootx-platform-core</artifactId>
                <version>${bootx-platform.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.bootx.platform</groupId>
                <artifactId>common-mybatis-plus</artifactId>
                <version>${bootx-platform.version}</version>
            </dependency>

            <!-- 第三方依赖版本管理 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
