spring:
  application:
    name: dax-pay-admin
  profiles:
    active: mysql
  # 解决Spring Boot 3.x与MyBatis-Plus Join扩展兼容性问题
  sql:
    init:
      dependency-detection:
        enabled: false
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          # MySQL连接
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************************************************************
          username: pay
          password: elt4RTckgPx.xgdg
      hikari:
        keepalive-time: 300000
        minimum-idle: 5
        maximum-pool-size: 20
        auto-commit: true
        idle-timeout: 30000
        pool-name: DaxPayHikariCP
        max-lifetime: 900000
        connection-timeout: 30000
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      database: 0
      password: "123@654"
      timeout: 10s
      lettuce:
        pool:
          max-wait: 1000ms
          max-active: 8
          max-idle: 8
          min-idle: 0
  task:
    scheduling:
      pool:
        size: 8
  servlet:
    multipart:
      # 上传文件大小限制为100M
      max-file-size: 100MB

# 服务器配置
server:
  port: 9999

# ORM
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  global-config:
    banner: false
    db-config:
      # PG逻辑删除需要指定为布尔值, 如果为0/1会出现查询类型错误
      logic-delete-value: true
      logic-not-delete-value: false

mybatis-plus-join:
  banner: false

# 安全框架
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Accesstoken
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  active-timeout: -1
  is-concurrent: true
  is-share: true
  is-log: false
  is-print: false

# 字段翻译插件
easy-trans:
  #启用平铺模式
  is-enable-tile: true

# 开发时显示debug日志
logging:
  level:
    cn.bootx.**: info
    cn.daxpay.**: info
    org.springframework.jdbc.core: debug
    root: info
  file:
    name: ./logs/daxpay.log

# 接口文档配置
springdoc:
  # 默认展开对象类型的属性, 主要用在get类型的参数中
  default-flat-param-object: true

# 基础脚手架配置
bootx-platform:
  config:
    deploy-mode: fusion
    client-codes:
      - dax-pay-admin
  common:
    # swagger相关配置
    swagger:
      author: sanfei
      title: 三飞科技支付平台
      description: 三飞科技支付平台
      version: 0.0.1
      base-packages:
        "[BootxPlatform接口]":
          - cn.bootx.platform.common
          - cn.bootx.platform.starter
          - cn.bootx.platform.iam
          - cn.bootx.platform.baseapi
          - cn.bootx.platform.notice
        "[支付平台接口]":
          - org.dromara.daxpay.controller
        "[支付通道接口]":
          - org.dromara.daxpay.channel
  starter:
    auth:
      enable-admin: true
      ignore-urls:
        - '/actuator/**'
        - '/v3/api-docs/**'
        - '/doc.html'
        - '/swagger-resources/**'
        - '/token/**'
        - '/ws/**'
        - '/demo/**'
        - '/test/**'
        - '/webjars/**'
        - '/front/**'
        - '/h5/**'
        - '/css/**'
        - '/error'
        - '/favicon.ico'
    file-upload:
      # 使用后端代理访问, 线上请使用 Nginx 配置或者直连方式，效率更高
      forward-server-url: http://127.0.0.1:9999
      file-server-url: http://127.0.0.1:9999

# DaxPay配置
dax-pay:
  env: DEV_
  machine-no: 70

# 文件存储配置
dromara:
  # 注意, 不要设置 domain 访问路径, 自行进行拼接访问路径, 来保证可迁移性
  x-file-storage:
    default-platform: local
    # 使用Nginx映射到存储路径, 然后将nginx的地址设置到 bootx-platform.starter.file-upload.file-server-url参数
    local-plus:
      - platform: local
        enable-storage: true
        base-path: /file/ # 基础路径
        storage-path: ./uploads # 存储路径 - 使用相对路径 